'use client';

import { useState } from 'react';
import { LayoutWrapper } from '@/components/LayoutWrapper';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapIcon, 
  CalendarIcon, 
  InfoIcon,
  ZoomInIcon,
  LayersIcon,
  NavigationIcon
} from 'lucide-react';

const historicalPeriods = [
  {
    id: 'slavs-settlement',
    title: 'Osídlenie Slovanov',
    period: '5.-6. storočie',
    description: 'Príchod a osídlenie slovanských kmeňov na území dnešného Slovenska',
    mapFeatures: [
      'Migračné trasy z východu',
      'Prvé slovanské osady',
      'Oblasti osídlenia okolo riek'
    ]
  },
  {
    id: 'samo-empire',
    title: '<PERSON>ova ríša',
    period: '623-658',
    description: '<PERSON>ze<PERSON><PERSON><PERSON> rozsah prvého slovanského štátneho útvaru',
    mapFeatures: [
      '<PERSON><PERSON><PERSON> Samovej ríše',
      '<PERSON>lavné centrá moci',
      'Obchodné cesty'
    ]
  },
  {
    id: 'great-moravia',
    title: 'Veľká Morava',
    period: '833-907',
    description: 'Najväčší územný rozsah slovanskej ríše v strednej Európe',
    mapFeatures: [
      'Maximálny územný rozsah',
      'Kresťanské centrá',
      'Školy a kostoly'
    ]
  },
  {
    id: 'hungarian-kingdom',
    title: 'Uhorské kráľovstvo',
    period: '1000-1918',
    description: 'Slovensko ako súčasť Uhorského kráľovstva',
    mapFeatures: [
      'Administratívne členenie',
      'Kráľovské mestá',
      'Hrady a opevnenia'
    ]
  }
];

export default function MapsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState<string | null>(null);

  return (
    <LayoutWrapper>
      <div className="max-w-7xl mx-auto pb-8">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold mb-2">Historické mapy Slovenska</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Preskúmajte vývoj slovenského územia naprieč storočiami pomocou interaktívnych máp
          </p>
        </div>

        {/* Coming Soon Notice */}
        <Card className="mb-8 border-dashed border-2">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapIcon className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-xl">Interaktívne mapy sa pripravujú</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground mb-4">
              Pracujeme na vytvorení interaktívnych historických máp, ktoré vám umožnia:
            </p>
            <div className="grid md:grid-cols-3 gap-4 mb-6">
              <div className="flex items-center gap-2 justify-center">
                <ZoomInIcon className="h-4 w-4 text-primary" />
                <span className="text-sm">Priblíženie a oddialenie</span>
              </div>
              <div className="flex items-center gap-2 justify-center">
                <LayersIcon className="h-4 w-4 text-primary" />
                <span className="text-sm">Prepínanie období</span>
              </div>
              <div className="flex items-center gap-2 justify-center">
                <NavigationIcon className="h-4 w-4 text-primary" />
                <span className="text-sm">Interaktívne body záujmu</span>
              </div>
            </div>
            <Button variant="outline" asChild>
              <a href="/historia">Späť na hlavnú stránku</a>
            </Button>
          </CardContent>
        </Card>

        {/* Preview of Historical Periods */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Plánované historické obdobia</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {historicalPeriods.map((period) => (
              <Card 
                key={period.id}
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  selectedPeriod === period.id ? 'ring-2 ring-primary' : ''
                }`}
                onClick={() => setSelectedPeriod(selectedPeriod === period.id ? null : period.id)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline">{period.period}</Badge>
                    <MapIcon className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <CardTitle className="text-lg">{period.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-3">{period.description}</p>
                  
                  {selectedPeriod === period.id && (
                    <div className="mt-4 p-3 bg-muted rounded-lg">
                      <h4 className="font-medium mb-2 flex items-center gap-2">
                        <InfoIcon className="h-4 w-4" />
                        Plánované funkcie mapy:
                      </h4>
                      <ul className="space-y-1">
                        {period.mapFeatures.map((feature, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Alternative Learning Options */}
        <Card>
          <CardHeader>
            <CardTitle>Medzitým si môžete vyskúšať</CardTitle>
            <p className="text-sm text-muted-foreground">
              Kým pracujeme na mapách, využite naše ďalšie spôsoby učenia histórie
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4">
              <Button asChild variant="outline" className="h-auto p-4 flex-col">
                <a href="/historia/stories">
                  <div className="text-lg font-semibold mb-1">Historické príbehy</div>
                  <div className="text-xs text-muted-foreground">Čítajte zaujímavé príbehy</div>
                </a>
              </Button>
              
              <Button asChild variant="outline" className="h-auto p-4 flex-col">
                <a href="/historia/timeline">
                  <div className="text-lg font-semibold mb-1">Časová os</div>
                  <div className="text-xs text-muted-foreground">Chronologický prehľad</div>
                </a>
              </Button>
              
              <Button asChild variant="outline" className="h-auto p-4 flex-col">
                <a href="/historia/quiz">
                  <div className="text-lg font-semibold mb-1">Kvíz</div>
                  <div className="text-xs text-muted-foreground">Otestujte svoje znalosti</div>
                </a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </LayoutWrapper>
  );
}
